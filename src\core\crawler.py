"""
Web crawling functionality
"""
import asyncio
import requests
import xml.etree.ElementTree as ET
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright

from src.config.settings import settings
from src.models.schemas import CrawlResult
from src.utils.text_processing import html_to_markdown
from src.utils.logging import get_logger

logger = get_logger(__name__)


class WebCrawler:
    """Web crawler for extracting content from websites"""
    
    def __init__(self, timeout: int = None):
        self.timeout = timeout or settings.crawl_timeout
        
    async def get_js_rendered_html(self, url: str) -> str:
        """Get HTML content using JavaScript rendering with Playwright"""
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            await page.goto(url, timeout=settings.js_render_timeout)
            content = await page.content()
            await browser.close()
            return content
    
    def extract_internal_links(self, base_url: str, html: str) -> List[str]:
        """Extract all internal links from HTML content"""
        soup = BeautifulSoup(html, 'html.parser')
        domain = urlparse(base_url).netloc
        links = set()
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            full_url = urljoin(base_url, href)
            parsed_url = urlparse(full_url)
            
            # Only include URLs from the same domain and exclude fragments
            if parsed_url.netloc == domain:
                # Remove fragments (#) from URLs
                clean_url = full_url.split('#')[0]
                # Remove trailing slash for consistency
                if clean_url.endswith('/') and clean_url != base_url + '/':
                    clean_url = clean_url[:-1]
                links.add(clean_url)
        
        return list(links)

    def discover_urls_from_sitemap(self, base_url: str) -> List[str]:
        """Discover URLs from XML sitemaps"""
        logger.info(f"Checking for sitemaps on {base_url}...")

        # Common sitemap locations to try
        sitemap_urls = [
            f"{base_url}/sitemap.xml",
            f"{base_url}/sitemap_index.xml",
            f"{base_url}/wp-sitemap.xml",
            f"{base_url}/sitemap-index.xml"
        ]

        # Also check robots.txt for sitemap references
        try:
            robots_url = f"{base_url}/robots.txt"
            response = requests.get(robots_url, timeout=self.timeout)
            if response.status_code == 200:
                for line in response.text.split('\n'):
                    if line.strip().lower().startswith('sitemap:'):
                        sitemap_url = line.split(':', 1)[1].strip()
                        if sitemap_url not in sitemap_urls:
                            sitemap_urls.append(sitemap_url)
                            logger.info(f"Found sitemap in robots.txt: {sitemap_url}")
        except Exception as e:
            logger.debug(f"Could not check robots.txt: {e}")

        all_urls = set()

        for sitemap_url in sitemap_urls:
            try:
                logger.info(f"Checking sitemap: {sitemap_url}")
                response = requests.get(sitemap_url, timeout=self.timeout)

                if response.status_code == 200:
                    urls = self._parse_sitemap_xml(response.content, base_url)
                    if urls:
                        logger.info(f"Found {len(urls)} URLs in {sitemap_url}")
                        all_urls.update(urls)

            except Exception as e:
                logger.debug(f"Failed to fetch sitemap {sitemap_url}: {e}")
                continue

        result = list(all_urls)
        if result:
            logger.info(f"Total URLs discovered from sitemaps: {len(result)}")
        else:
            logger.info("No URLs found in sitemaps")

        return result

    def _parse_sitemap_xml(self, xml_content: bytes, base_url: str) -> List[str]:
        """Parse XML sitemap content and extract URLs"""
        try:
            root = ET.fromstring(xml_content)
            urls = []

            # Handle sitemap index (contains references to other sitemaps)
            if 'sitemapindex' in root.tag:
                logger.info("Found sitemap index, checking sub-sitemaps...")
                for sitemap in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap'):
                    loc_elem = sitemap.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc_elem is not None:
                        sub_sitemap_url = loc_elem.text
                        try:
                            response = requests.get(sub_sitemap_url, timeout=self.timeout)
                            if response.status_code == 200:
                                sub_urls = self._parse_sitemap_xml(response.content, base_url)
                                urls.extend(sub_urls)
                        except Exception as e:
                            logger.debug(f"Failed to fetch sub-sitemap {sub_sitemap_url}: {e}")

            # Handle regular sitemap (contains actual URLs)
            else:
                for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                    loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc_elem is not None:
                        url = loc_elem.text
                        # Only include URLs from the same domain
                        if url and urlparse(url).netloc == urlparse(base_url).netloc:
                            urls.append(url)

            return urls

        except ET.ParseError as e:
            logger.debug(f"Failed to parse sitemap XML: {e}")
            return []
        except Exception as e:
            logger.debug(f"Error processing sitemap: {e}")
            return []

    def crawl_url(self, url: str) -> Optional[CrawlResult]:
        """Crawl a single URL and extract content"""
        try:
            response = requests.get(url, timeout=self.timeout)
            if response.status_code != 200:
                logger.warning(f"Skipping dead link: {url} (Status code: {response.status_code})")
                return None
            
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            title = soup.title.string if soup.title else 'No title'
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            h1 = soup.find('h1')
            
            return CrawlResult(
                url=url,
                title=title,
                description=meta_desc['content'] if meta_desc else '',
                h1=h1.text if h1 else '',
                text=html_to_markdown(html_content),
                raw_html=html_content
            )
        except Exception as e:
            logger.error(f"Error crawling {url}: {e}")
            return None
    
    async def crawl_url_with_js(self, url: str) -> Optional[CrawlResult]:
        """Crawl a URL using JavaScript rendering as fallback"""
        try:
            logger.info(f"Trying JS rendering for {url}...")
            html = await self.get_js_rendered_html(url)
            soup = BeautifulSoup(html, 'html.parser')
            
            text = html_to_markdown(html)
            
            return CrawlResult(
                url=url,
                title=soup.title.string if soup.title else 'No title',
                description=soup.find('meta', attrs={'name': 'description'})['content'] 
                           if soup.find('meta', attrs={'name': 'description'}) else '',
                h1=soup.find('h1').text if soup.find('h1') else '',
                text=text,
                raw_html=html
            )
        except Exception as e:
            logger.error(f"JS rendering failed for {url}: {e}")
            return None
    
    async def crawl_site(self, urls: List[str], output_dir: str) -> List[CrawlResult]:
        """Crawl multiple URLs and return results"""
        results = []
        failed_urls = []

        for url in urls:
            data = self.crawl_url(url)
            if not data:
                # Try JavaScript rendering as fallback
                data = await self.crawl_url_with_js(url)
                if not data:
                    failed_urls.append(url)
                    continue

            results.append(data)

        # Save failed URLs for debugging
        if failed_urls:
            import os
            with open(os.path.join(output_dir, 'failed_urls.txt'), 'w') as f:
                for url in failed_urls:
                    f.write(url + '\n')

        return results
    
    async def discover_urls_from_homepage(self, homepage: str) -> List[str]:
        """Discover all internal URLs using comprehensive strategy: sitemaps first, then homepage links, then fallbacks"""
        logger.info(f"Discovering URLs from {homepage}...")

        # Extract base URL for sitemap discovery
        parsed_url = urlparse(homepage)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

        # Method 1: Try sitemaps first (most comprehensive)
        try:
            logger.info("Method 1: Checking sitemaps...")
            sitemap_urls = self.discover_urls_from_sitemap(base_url)

            if sitemap_urls and len(sitemap_urls) > 1:
                # Add homepage if not already included
                if homepage not in sitemap_urls and base_url not in sitemap_urls:
                    sitemap_urls.insert(0, homepage)

                logger.info(f"✅ Sitemap discovery successful: {len(sitemap_urls)} URLs found")
                return sitemap_urls
            else:
                logger.info("Sitemaps found limited URLs, trying homepage link extraction...")

        except Exception as e:
            logger.warning(f"Sitemap discovery failed: {e}, trying homepage link extraction...")

        # Method 2: Try BeautifulSoup homepage link extraction
        try:
            logger.info("Method 2: Extracting links from homepage with BeautifulSoup...")
            response = requests.get(homepage, timeout=self.timeout)
            if response.status_code == 200:
                html = response.text
                links = self.extract_internal_links(homepage, html)

                # Add the homepage itself and remove duplicates
                all_urls = [homepage] + links
                unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order

                logger.info(f"BeautifulSoup discovered {len(unique_urls)} URLs from homepage")

                # If we found a reasonable number of links, use this result
                if len(unique_urls) > 1:  # More than just the homepage
                    return unique_urls
                else:
                    logger.info("BeautifulSoup found limited URLs, trying Playwright as fallback...")
            else:
                logger.warning(f"BeautifulSoup failed with status {response.status_code}, trying Playwright...")

        except Exception as e:
            logger.warning(f"BeautifulSoup failed: {e}, trying Playwright as fallback...")

        # Method 3: Fallback to Playwright for JavaScript-heavy sites
        try:
            logger.info("Method 3: Trying Playwright for JavaScript-rendered content...")
            html = await self.get_js_rendered_html(homepage)

            # Extract all internal links
            links = self.extract_internal_links(homepage, html)

            # Add the homepage itself and remove duplicates
            all_urls = [homepage] + links
            unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order

            logger.info(f"Playwright discovered {len(unique_urls)} URLs")
            return unique_urls

        except Exception as e:
            logger.error(f"All URL discovery methods failed: {e}")
            logger.info("Falling back to homepage only")
            return [homepage]
