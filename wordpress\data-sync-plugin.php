<?php
/**
 * Plugin Name: WP Data Exporter & Sync Trigger
 * Description: Consolidates content analysis data (publish dates, SEO, internal links) and provides a REST API & external script trigger.
 * Version:     1.0.0
 * Author:      <PERSON><PERSON><PERSON>
 * License:     GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

define('WPDEST_PLUGIN_SLUG', 'wp_data_exporter_sync_trigger');
define('WPDEST_API_NAMESPACE', 'wp-data-exporter/v1');

class WP_Data_Exporter_Sync_Trigger {

    private $options;
    private $option_name = WPDEST_PLUGIN_SLUG . '_settings';

    public function __construct() {
        // Define default options, including those ported from other plugins
        $defaults = [
            'plugin_api_key' => '',
            'external_script_trigger_url' => '',
            'external_script_api_key' => '', // Optional: API key for the Python script service
            // Settings from content-internal-links
            'cil_excluded_texts' => implode("\n", $this->get_default_excluded_texts()),
            'cil_excluded_urls' => implode("\n", $this->get_default_excluded_urls()),
            'cil_exclude_mailto' => '1',
            'cil_exclude_tel' => '1',
            'cil_exclude_sms' => '1',
            'cil_excluded_post_types' => '', // Default is empty, relies on get_all_content_post_types logic
            // Settings from aioseo-publish-dates
            'aioseo_publish_dates_outdated_threshold' => 6,
            'aioseo_publish_dates_seo_priority' => 'aioseo', // Default priority
        ];

        // Get saved options and merge with defaults
        $this->options = get_option($this->option_name, $defaults);
        $this->options = wp_parse_args($this->options, $defaults);

        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('rest_api_init', [$this, 'register_rest_routes']);

        // Add styles and scripts for settings page tooltips
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        add_action('admin_footer', [$this, 'add_admin_tooltip_script']);

    }


    public function add_admin_menu() {
        add_options_page(
            'WP Data Exporter & Sync Trigger',
            'Data Exporter & Trigger',
            'manage_options',
            WPDEST_PLUGIN_SLUG,
            [$this, 'create_settings_page']
        );
    }

    public function register_settings() {
        register_setting(
            WPDEST_PLUGIN_SLUG . '_settings_group',
            $this->option_name,
            [$this, 'sanitize_settings']
        );
    }

    public function sanitize_settings($input) {
        $sanitized = $this->options; // Start with existing options

        // API Settings
        if (isset($input['plugin_api_key'])) {
            $sanitized['plugin_api_key'] = sanitize_text_field($input['plugin_api_key']);
        }
        if (isset($input['external_script_trigger_url'])) {
            $sanitized['external_script_trigger_url'] = esc_url_raw($input['external_script_trigger_url']);
        }
        if (isset($input['external_script_api_key'])) {
            $sanitized['external_script_api_key'] = sanitize_text_field($input['external_script_api_key']);
        }

        // Content Analysis Settings (from content-internal-links)
        if (isset($input['cil_excluded_texts'])) {
            $sanitized['cil_excluded_texts'] = sanitize_textarea_field($input['cil_excluded_texts']);
        }
        if (isset($input['cil_excluded_urls'])) {
            $sanitized['cil_excluded_urls'] = sanitize_textarea_field($input['cil_excluded_urls']);
        }
        $sanitized['cil_exclude_mailto'] = isset($input['cil_exclude_mailto']) ? '1' : '0';
        $sanitized['cil_exclude_tel'] = isset($input['cil_exclude_tel']) ? '1' : '0';
        $sanitized['cil_exclude_sms'] = isset($input['cil_exclude_sms']) ? '1' : '0';
        if (isset($input['cil_excluded_post_types'])) {
            $sanitized['cil_excluded_post_types'] = sanitize_textarea_field($input['cil_excluded_post_types']);
        }

        // Publish Date & SEO Settings (from aioseo-publish-dates)
        if (isset($input['aioseo_publish_dates_outdated_threshold'])) {
            $threshold = intval($input['aioseo_publish_dates_outdated_threshold']);
            $sanitized['aioseo_publish_dates_outdated_threshold'] = max(1, min(60, $threshold)); // Clamp between 1 and 60
        }
        if (isset($input['aioseo_publish_dates_seo_priority'])) {
             // Validate against active plugins if possible, otherwise sanitize
             $active_plugins = $this->get_active_seo_plugins();
             if (isset($active_plugins[$input['aioseo_publish_dates_seo_priority']])) {
                 $sanitized['aioseo_publish_dates_seo_priority'] = sanitize_text_field($input['aioseo_publish_dates_seo_priority']);
             } else {
                 // Fallback to a default or current if selected invalid
                 $sanitized['aioseo_publish_dates_seo_priority'] = $this->options['aioseo_publish_dates_seo_priority'];
             }
        }

        return $sanitized;
    }

    public function create_settings_page() {
        ?>
        <div class="wrap">
            <h1>WP Data Exporter & Sync Trigger Settings</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields(WPDEST_PLUGIN_SLUG . '_settings_group');
                do_settings_sections(WPDEST_PLUGIN_SLUG . '_settings_group'); // Not strictly needed if no sections defined
                $options = $this->options;
                ?>
                <div class="settings-section">
                    <h2>API & External Trigger Settings</h2>
                    <table class="form-table">
                        <tr valign="top">
                            <th scope="row">Plugin API Key</th>
                            <td>
                                <input type="text" name="<?php echo esc_attr($this->option_name); ?>[plugin_api_key]" value="<?php echo esc_attr($options['plugin_api_key']); ?>" class="regular-text" />
                                <button type="button" onclick="document.querySelector('[name=\'<?php echo esc_attr($this->option_name); ?>[plugin_api_key]\']').value = '<?php echo esc_attr(wp_generate_password(32, false)); ?>'" class="button">Generate Key</button>
                                <p class="description">API key for external scripts (like your Python service) to access this plugin's <code>/data</code> and <code>/trigger-external-script</code> endpoints. Provide this key in the 'X-Plugin-API-Key' header.</p>
                            </td>
                        </tr>
                        <tr valign="top">
                            <th scope="row">External Python Script Trigger URL</th>
                            <td>
                                <input type="url" name="<?php echo esc_attr($this->option_name); ?>[external_script_trigger_url]" value="<?php echo esc_attr($options['external_script_trigger_url']); ?>" class="regular-text" placeholder="https://your-python-service.com/trigger-sync" />
                                <p class="description">The URL of your Python script's service endpoint that this plugin will call to trigger a sync.</p>
                            </td>
                        </tr>
                         <tr valign="top">
                            <th scope="row">API Key for External Python Script (Optional)</th>
                            <td>
                                <input type="text" name="<?php echo esc_attr($this->option_name); ?>[external_script_api_key]" value="<?php echo esc_attr($options['external_script_api_key']); ?>" class="regular-text" />
                                <p class="description">If your Python script's trigger URL requires an API key, enter it here. This plugin will send it in the 'X-External-API-Key' header.</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="settings-section">
                    <h2>Content Analysis Settings</h2>
                    <table class="form-table">
                         <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($this->option_name); ?>[cil_excluded_post_types]">Excluded Post Types</label>
                                <span class="tooltip" title="Enter post type slugs to exclude from scanning, one per line. These post types will not be scanned for links or included in publish/SEO data.">
                                    <span class="dashicons dashicons-info"></span>
                                </span>
                            </th>
                            <td>
                                <textarea name="<?php echo esc_attr($this->option_name); ?>[cil_excluded_post_types]" id="cil_excluded_post_types" rows="5" cols="50" placeholder="iso<?php echo "\n";?>landing-page<?php echo "\n";?>testimonial"><?php echo esc_textarea($options['cil_excluded_post_types']); ?></textarea>
                                <p class="description">Enter one post type slug per line. These post types will be completely excluded from scanning and data collection.</p>
                                <div class="post-type-list">
                                    <strong>Available public post types on your site:</strong><br>
                                    <?php
                                    $all_public_post_types_objects = get_post_types(['public' => true], 'objects');
                                    foreach ($all_public_post_types_objects as $post_type => $post_type_obj): ?>
                                        <span class="post-type-item"><?php echo esc_html($post_type); ?> (<?php echo esc_html($post_type_obj->label); ?>)</span>
                                    <?php endforeach; ?>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label>Exclude Special Links</label>
                                <span class="tooltip" title="Choose which special link types to exclude from scanning. Uncheck to include them in your link analysis.">
                                    <span class="dashicons dashicons-info"></span>
                                </span>
                            </th>
                            <td>
                                <div class="checkbox-group">
                                    <label>
                                        <input type="checkbox" name="<?php echo esc_attr($this->option_name); ?>[cil_exclude_mailto]" value="1" <?php checked($options['cil_exclude_mailto'], '1'); ?>>
                                        Exclude Email links (mailto:)
                                    </label>
                                    <label>
                                        <input type="checkbox" name="<?php echo esc_attr($this->option_name); ?>[cil_exclude_tel]" value="1" <?php checked($options['cil_exclude_tel'], '1'); ?>>
                                        Exclude Phone links (tel:)
                                    </label>
                                    <label>
                                        <input type="checkbox" name="<?php echo esc_attr($this->option_name); ?>[cil_exclude_sms]" value="1" <?php checked($options['cil_exclude_sms'], '1'); ?>>
                                        Exclude SMS links (sms:)
                                    </label>
                                </div>
                                <p class="description">Uncheck any box to include those link types in your scan results.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($this->option_name); ?>[cil_excluded_texts]">Excluded Anchor Texts</label>
                                <span class="tooltip" title="Enter anchor texts to exclude from the link scan, one per line. These are typically generic call-to-action texts that don't provide meaningful link information.">
                                    <span class="dashicons dashicons-info"></span>
                                </span>
                            </th>
                            <td>
                                <textarea name="<?php echo esc_attr($this->option_name); ?>[cil_excluded_texts]" id="cil_excluded_texts" rows="8" cols="50"><?php echo esc_textarea($options['cil_excluded_texts']); ?></textarea>
                                <p class="description">Enter one anchor text per line. These links will be ignored during scanning.</p>
                                <p class="reset-defaults">Default values: <?php echo esc_html(implode(', ', $this->get_default_excluded_texts())); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($this->option_name); ?>[cil_excluded_urls]">Excluded URL Patterns</label>
                                <span class="tooltip" title="Enter URL patterns to exclude from the link scan, one per line. Any link containing these patterns will be ignored. Wildcards (*) are supported.">
                                    <span class="dashicons dashicons-info"></span>
                                </span>
                            </th>
                            <td>
                                <textarea name="<?php echo esc_attr($this->option_name); ?>[cil_excluded_urls]" id="cil_excluded_urls" rows="8" cols="50"><?php echo esc_textarea($options['cil_excluded_urls']); ?></textarea>
                                <p class="description">Enter one URL pattern per line. Any link containing these patterns will be excluded from results.</p>
                                <div class="wildcard-info">
                                    <strong>💡 Pattern Matching Tips:</strong><br>
                                    • Simple text: <code>/blog/</code> - excludes any URL containing "/blog/"<br>
                                    • Wildcard: <code>/iso/*</code> - excludes all URLs starting with "/iso/"<br>
                                    • Multiple wildcards: <code>/category/*/page/*</code> - excludes paginated category pages<br>
                                    • The * wildcard matches any characters (e.g., <code>example.com/path/*</code>)
                                </div>
                                <p class="reset-defaults">Default values: <?php echo esc_html(implode(', ', $this->get_default_excluded_urls())); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="settings-section">
                    <h2>Publish Date & SEO Settings</h2>
                    <table class="form-table">
                         <tr>
                            <th scope="row">Outdated Content Threshold</th>
                            <td>
                                <input type="number" name="<?php echo esc_attr($this->option_name); ?>[aioseo_publish_dates_outdated_threshold]" value="<?php echo esc_attr($options['aioseo_publish_dates_outdated_threshold']); ?>" min="1" max="60" style="width: 80px;" />
                                <span> months</span>
                                <p class="description">Content older than this many months will be considered outdated.</p>
                            </td>
                        </tr>
                         <tr>
                            <th scope="row">SEO Plugin Priority</th>
                            <td>
                                <select name="<?php echo esc_attr($this->option_name); ?>[aioseo_publish_dates_seo_priority]" style="width: 300px;">
                                    <?php
                                    $active_plugins = $this->get_active_seo_plugins();
                                    if (empty($active_plugins)) { $active_plugins['none'] = 'No SEO plugins active'; }

                                    $current_priority = $options['aioseo_publish_dates_seo_priority'];
                                    foreach ($active_plugins as $plugin_key => $plugin_name) {
                                        echo '<option value="' . esc_attr($plugin_key) . '" ' .
                                             selected($current_priority, $plugin_key, false) . '>' .
                                             esc_html($plugin_name) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="description">Select which SEO plugin's data should be prioritized when multiple plugins are active.</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="settings-section">
                    <h2>Always Excluded (Not Configurable)</h2>
                    <ul>
                        <li>• Pagination links (e.g., /page/2/)</li>
                        <li>• Table of Contents links (#elementor-toc, #ez-toc, etc.)</li>
                        <li>• Links inside <code>&lt;nav&gt;</code> elements</li>
                        <li>• Links inside <code>&lt;button&gt;</code> elements</li>
                        <li>• Links with common button classes (e.g., <code>btn</code>, <code>uc_more_btn</code>)</li>
                        <li>• Posts with AIOSEO 'noindex' robots meta</li>
                    </ul>
                </div>

                <?php submit_button(); ?>
            </form>
        </div>
        <style>
            .settings-section {
                background: #fff;
                padding: 20px;
                margin: 20px 0;
                border: 1px solid #ccc;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                border-radius: 4px;
            }
            .settings-section h2 {
                margin-top: 0;
                padding-bottom: 10px;
                border-bottom: 1px solid #ddd;
            }
            .form-table th { width: 200px; padding-right: 20px; }
            .form-table td { width: calc(100% - 220px); }
            .form-table textarea,
            .form-table input[type="text"],
            .form-table input[type="url"],
            .form-table select { width: 100%; max-width: 500px; }
            .tooltip {
                display: inline-block;
                margin-left: 5px;
                color: #666;
                cursor: help;
            }
            .tooltip .dashicons {
                font-size: 16px;
                width: 16px;
                height: 16px;
                line-height: 16px;
            }
            .description {
                color: #666;
                font-style: italic;
                margin-top: 5px;
                display: block;
            }
            .reset-defaults {
                margin-top: 10px;
                color: #666;
                font-size: 12px;
            }
            .checkbox-group, .radio-group {
                margin: 15px 0;
            }
            .checkbox-group label, .radio-group label {
                display: block;
                margin-bottom: 10px;
            }
            .checkbox-group input[type="checkbox"], .radio-group input[type="radio"] {
                margin-right: 8px;
            }
            .post-type-list {
                background: #f5f5f5;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
                font-size: 12px;
                max-height: 150px;
                overflow-y: auto;
            }
            .post-type-item {
                display: inline-block;
                margin: 2px 5px;
                padding: 2px 6px;
                background: #e0e0e0;
                border-radius: 3px;
            }
            .wildcard-info {
                background-color: #e3f2fd;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
                border-left: 4px solid #2196f3;
                font-size: 13px;
            }
        </style>
        <?php
    }

    public function enqueue_admin_assets($hook) {
        if ('settings_page_' . WPDEST_PLUGIN_SLUG === $hook) {
            // Enqueue jQuery UI Tooltip
            wp_enqueue_script('jquery-ui-tooltip');
            wp_enqueue_style('jquery-ui-css', 'https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css');
        }
    }

    public function add_admin_tooltip_script() {
        $screen = get_current_screen();
        if ($screen && 'settings_page_' . WPDEST_PLUGIN_SLUG === $screen->base) {
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                if (typeof $.fn.tooltip === 'function') {
                    $('.tooltip').tooltip({
                        content: function () {
                            return $(this).prop('title');
                        },
                        show: { effect: "blind", duration: 200 },
                        hide: { effect: "blind", duration: 200 },
                        position: { my: "center bottom-10", at: "center top", using: function( position, feedback ) {
                            $( this ).css( position );
                            $( "<div>" )
                                .addClass( "arrow" )
                                .addClass( feedback.vertical )
                                .addClass( feedback.horizontal )
                                .appendTo( this );
                            }
                        }
                    });
                }
            });
            </script>
            <style>
                /* Tooltip styles (basic, match WP admin) */
                .ui-tooltip, .arrow { position: absolute; left: -9999px; }
                .ui-tooltip {
                    padding: 8px;
                    margin-top: 5px;
                    font-size: 12px;
                    background-color: #32373c;
                    color: #fff;
                    border-radius: 4px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                    max-width: 300px;
                    word-wrap: break-word;
                    z-index: 9999;
                }
                .arrow {
                    width: 70px;
                    height: 16px;
                    overflow: hidden;
                    position: absolute;
                    left: 50%;
                    margin-left: -35px;
                    bottom: -16px;
                }
                .arrow.top { top: -16px; bottom: auto; }
                .arrow.bottom:after { border-top-color: #32373c; bottom: 0; }
                .arrow:after {
                    content: "";
                    position: absolute;
                    left: 50%;
                    margin-left: -8px;
                    border: 8px solid transparent;
                }
                .arrow.top:after { border-bottom-color: #32373c; top: 0; }
                .arrow.bottom:after { border-top-color: #32373c; bottom: 0; }
            </style>
            <?php
        }
    }

    public function register_rest_routes() {
        register_rest_route(WPDEST_API_NAMESPACE, '/data', [
            'methods' => 'GET',
            'callback' => [$this, 'get_site_data_callback'],
            'permission_callback' => [$this, 'api_permission_check'],
        ]);

        register_rest_route(WPDEST_API_NAMESPACE, '/trigger-external-script', [
            'methods' => 'POST',
            'callback' => [$this, 'trigger_external_script_callback'],
            'permission_callback' => [$this, 'api_permission_check'], // Secure this endpoint too
        ]);
    }

    public function api_permission_check(WP_REST_Request $request) {
        $sent_key = $request->get_header('X-Plugin-API-Key');
        $stored_key = $this->options['plugin_api_key'];

        if (empty($stored_key)) {
            return new WP_Error('rest_api_key_not_set', 'API key for this plugin is not configured in WordPress settings.', ['status' => 500]);
        }
        if (empty($sent_key)) {
            return new WP_Error('rest_api_key_missing', 'API key is missing in X-Plugin-API-Key header.', ['status' => 401]);
        }
        if (!hash_equals($stored_key, $sent_key)) {
            return new WP_Error('rest_api_key_invalid', 'Invalid API key.', ['status' => 403]);
        }
        return true;
    }

    // --- Default Exclusions (from content-internal-links) ---
    private function get_default_excluded_texts() { return ['Read More', 'Continue Reading', 'Learn More', 'View More', 'Get in touch']; }
    private function get_default_excluded_urls() { return ['/blog/', '/news/', '/insurance-glossary/', '/glossary/', '/wp-admin/']; }
    private function get_default_excluded_post_types_system() { return ['attachment', 'revision', 'nav_menu_item', 'custom_css', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation', 'elementor_library', 'jet-menu', 'e-landing-page', 'e-floating-buttons', 'wpcode', 'elementor_snippet', 'aiosrs-schema', 'wpcode-blocks', 'elementor_font', 'elementor_icons']; }

    // --- Data Aggregation Functions ---
private function get_publish_seo_data() {
    global $wpdb;
    $data = [];
    $outdated_threshold = $this->options['aioseo_publish_dates_outdated_threshold'];

    $args = [
        'post_type' => $this->get_all_content_post_types(),
        'post_status' => 'publish',
        'posts_per_page' => -1,
    ];

    if (empty($args['post_type'])) {
         return [];
    }

    $post_types_placeholders = implode(',', array_fill(0, count($args['post_type']), '%s'));
    $params = $args['post_type'];

    // Default query: all published posts of allowed types, no extra noindex filtering
    $query_sql = "
        SELECT p.ID, p.post_type, p.post_title, p.post_date, p.post_modified, p.post_content
        FROM {$wpdb->posts} p
        WHERE p.post_type IN ($post_types_placeholders)
        AND p.post_status = 'publish'
    ";

    // AIOSEO filtering: exclude posts with noindex via postmeta (v4+ method, always safe)
    if ($this->is_seo_plugin_active('aioseo')) {
        $query_sql = "
            SELECT p.ID, p.post_type, p.post_title, p.post_date, p.post_modified, p.post_content
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_aioseo_robots_meta'
            WHERE p.post_type IN ($post_types_placeholders)
            AND p.post_status = 'publish'
            AND (pm.meta_value NOT LIKE %s OR pm.meta_value IS NULL)
        ";
        $params[] = '%"noindex";%';
    }

    // Query the posts
    $posts = $wpdb->get_results($wpdb->prepare($query_sql, ...$params));

    foreach ($posts as $post) {
        $post_id = $post->ID;
        $date_modified_unix = strtotime($post->post_modified);
        $threshold_ago = strtotime("-{$outdated_threshold} months");
        $update_status = ($date_modified_unix < $threshold_ago) ? "Outdated" : "Recent";

        $categories = get_the_category($post_id);
        $category_list = ($categories && $post->post_type === 'post') ? implode(', ', wp_list_pluck($categories, 'name')) : 'N/A';

        // Get SEO data for this post
        $seo_data = $this->get_seo_data_for_post($post_id, $post);

        $data[] = [
            'post_id' => $post_id,
            'url' => get_permalink($post_id),
            'title' => $seo_data['title'], // Use SEO title if available, fallback to post title
            'description' => $seo_data['description'], // Meta description
            'h1' => $seo_data['h1'], // H1 tag
            'text' => $seo_data['content'], // Page content (cleaned)
            'type' => $post->post_type,
            'category' => $category_list,
            'publish_date' => date('Y-m-d', strtotime($post->post_date)),
            'last_updated' => date('Y-m-d', $date_modified_unix),
            'update_status' => $update_status,
            'days_since_update' => floor((time() - $date_modified_unix) / (60 * 60 * 24)),
            'focus_keyphrase' => $this->get_focus_keyword_for_post($post_id),
        ];
    }
    return $data;
}


    // --- SEO Content Extraction ---
    private function get_seo_data_for_post($post_id, $post) {
        // Get SEO title (from SEO plugins or fallback to post title)
        $seo_title = $this->get_seo_title_for_post($post_id) ?: $post->post_title;

        // Get meta description (from SEO plugins)
        $meta_description = $this->get_meta_description_for_post($post_id);

        // Get H1 tag (extract from content or use title as fallback)
        $h1_tag = $this->extract_h1_from_content($post->post_content) ?: $seo_title;

        // Get cleaned page content (strip HTML, shortcodes, etc.)
        $page_content = $this->get_cleaned_content($post->post_content);

        return [
            'title' => $seo_title,
            'description' => $meta_description,
            'h1' => $h1_tag,
            'content' => $page_content
        ];
    }

    private function get_seo_title_for_post($post_id) {
        global $wpdb;

        // Try AIOSEO first
        if ($this->is_seo_plugin_active('aioseo')) {
            $aioseoTableName = $wpdb->prefix . 'aioseo_posts';
            if ($wpdb->get_var("SHOW TABLES LIKE '$aioseoTableName'") == $aioseoTableName) {
                $title = $wpdb->get_var($wpdb->prepare("SELECT title FROM {$aioseoTableName} WHERE post_id = %d", $post_id));
                if ($title) {
                    return htmlspecialchars_decode($title);
                }
            }
        }

        // Try Yoast SEO
        if ($this->is_seo_plugin_active('yoast')) {
            $title = get_post_meta($post_id, '_yoast_wpseo_title', true);
            if ($title) {
                return htmlspecialchars_decode($title);
            }
        }

        // Try RankMath
        if ($this->is_seo_plugin_active('rankmath')) {
            $title = get_post_meta($post_id, 'rank_math_title', true);
            if ($title) {
                return htmlspecialchars_decode($title);
            }
        }

        return null; // Will fallback to post title
    }

    private function get_meta_description_for_post($post_id) {
        global $wpdb;

        // Try AIOSEO first
        if ($this->is_seo_plugin_active('aioseo')) {
            $aioseoTableName = $wpdb->prefix . 'aioseo_posts';
            if ($wpdb->get_var("SHOW TABLES LIKE '$aioseoTableName'") == $aioseoTableName) {
                $description = $wpdb->get_var($wpdb->prepare("SELECT description FROM {$aioseoTableName} WHERE post_id = %d", $post_id));
                if ($description) {
                    return htmlspecialchars_decode($description);
                }
            }
        }

        // Try Yoast SEO
        if ($this->is_seo_plugin_active('yoast')) {
            $description = get_post_meta($post_id, '_yoast_wpseo_metadesc', true);
            if ($description) {
                return htmlspecialchars_decode($description);
            }
        }

        // Try RankMath
        if ($this->is_seo_plugin_active('rankmath')) {
            $description = get_post_meta($post_id, 'rank_math_description', true);
            if ($description) {
                return htmlspecialchars_decode($description);
            }
        }

        return ''; // Empty if no meta description found
    }

    private function extract_h1_from_content($content) {
        // Remove shortcodes first
        $content = strip_shortcodes($content);

        // Look for H1 tags in the content
        if (preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $content, $matches)) {
            return strip_tags($matches[1]);
        }

        return null; // Will fallback to title
    }

    private function get_cleaned_content($content) {
        // Apply WordPress content filters (shortcodes, etc.)
        $content = apply_filters('the_content', $content);

        // Strip HTML tags but preserve some structure
        $content = strip_tags($content);

        // Clean up whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        $content = trim($content);

        // Limit content length for database efficiency (first 5000 chars)
        if (strlen($content) > 5000) {
            $content = substr($content, 0, 5000) . '...';
        }

        return $content;
    }

    // --- SEO Keyword Logic (from aioseo-publish-dates) ---
    private function get_focus_keyword_for_post($post_id) {
        global $wpdb;
        $priority_plugin = $this->options['aioseo_publish_dates_seo_priority'];

        $plugin_order = array();
        $plugin_order[] = $priority_plugin;

        $default_order = array('aioseo', 'rankmath', 'yoast');
        foreach ($default_order as $plugin) {
            if ($plugin !== $priority_plugin && $this->is_seo_plugin_active($plugin)) {
                $plugin_order[] = $plugin;
            }
        }

        foreach ($plugin_order as $plugin) {
            switch ($plugin) {
                case 'aioseo':
                    // Check if AIOSEO is active before querying its table
                    if ($this->is_seo_plugin_active('aioseo')) {
                        $aioseoTableName = $wpdb->prefix . 'aioseo_posts';
                        if ($wpdb->get_var("SHOW TABLES LIKE '$aioseoTableName'") == $aioseoTableName) {
                            $keyphrases_data = $wpdb->get_var($wpdb->prepare("SELECT keyphrases FROM {$aioseoTableName} WHERE post_id = %d", $post_id));
                            if ($keyphrases_data) {
                                $keyphrases = json_decode($keyphrases_data, true);
                                if (isset($keyphrases['focus']['keyphrase'])) {
                                    return htmlspecialchars_decode($keyphrases['focus']['keyphrase']);
                                }
                            }
                        }
                    }
                    break;

                case 'rankmath':
                    if ($this->is_seo_plugin_active('rankmath')) {
                        $rankmath_primary_keyword = get_post_meta($post_id, 'rank_math_focus_keyword', true);
                        if (!empty($rankmath_primary_keyword)) {
                            return $rankmath_primary_keyword;
                        }
                    }
                    break;

                case 'yoast':
                    if ($this->is_seo_plugin_active('yoast')) {
                        $yoast_primary_keyword = get_post_meta($post_id, '_yoast_wpseo_focuskw', true);
                        if (!empty($yoast_primary_keyword)) {
                            return $yoast_primary_keyword;
                        }
                    }
                    break;
            }
        }

        return 'Not Set';
    }

    private function is_seo_plugin_active($plugin_name) {
        switch ($plugin_name) {
            case 'aioseo':
                return class_exists('AIOSEO') || function_exists('aioseo');
            case 'rankmath':
                return class_exists('RankMath') || function_exists('rank_math');
            case 'yoast':
                return class_exists('WPSEO_Utils') || function_exists('wpseo_init');
            default:
                return false;
        }
    }

    private function get_active_seo_plugins() {
         $active = [];
         if ($this->is_seo_plugin_active('aioseo')) $active['aioseo'] = 'All In One SEO';
         if ($this->is_seo_plugin_active('rankmath')) $active['rankmath'] = 'RankMath';
         if ($this->is_seo_plugin_active('yoast')) $active['yoast'] = 'Yoast SEO';
         return $active;
    }

    // --- Internal Links Logic (from content-internal-links v3.2.1) ---
    private function get_internal_links_data_for_site() {
        global $wpdb;
        $site_url = get_site_url();
        $results = [];

        // Get exclusions from settings
        $excluded_texts = $this->get_excluded_texts();
        $excluded_urls = $this->get_excluded_urls();

        $post_types_to_scan = $this->get_all_content_post_types();

        if (empty($post_types_to_scan)) {
            return [];
        }

        $post_types_placeholders = implode(',', array_fill(0, count($post_types_to_scan), '%s'));

        // Query posts, excluding those with noindex meta from AIOSEO if present
        $query_sql = "
            SELECT p.ID, p.post_content, p.post_type, p.post_title
            FROM {$wpdb->posts} p
            WHERE p.post_type IN ($post_types_placeholders)
            AND p.post_status = 'publish'
        ";

        // Conditionally add JOIN and WHERE clause for AIOSEO noindex if AIOSEO is active
        if ($this->is_seo_plugin_active('aioseo')) {
             $aioseoTableName = $wpdb->prefix . 'aioseo_posts';
             // Check if the aioseo_posts table exists before joining
             if ($wpdb->get_var("SHOW TABLES LIKE '$aioseoTableName'") == $aioseoTableName) {
				$query_sql = "
					SELECT p.ID, p.post_content, p.post_type, p.post_title
					FROM {$wpdb->posts} p
					LEFT JOIN {$wpdb->postmeta} pm 
						ON p.ID = pm.post_id AND pm.meta_key = '_aioseo_robots_meta'
					WHERE p.post_type IN ($post_types_placeholders)
					AND p.post_status = 'publish'
					AND (pm.meta_value NOT LIKE '%\"noindex\";%' OR pm.meta_value IS NULL)
				";
             }
        }

        $posts = $wpdb->get_results($wpdb->prepare(
            $query_sql,
            $post_types_to_scan
        ));

        foreach ($posts as $post) {
            // Clean content by removing links within nav/button elements and specific classes
            $content_without_nav = preg_replace('/<nav[^>]*>.*?<\/nav>/s', '', $post->post_content);
            $content_without_buttons = preg_replace('/<button[^>]*>.*?<\/button>/s', '', $content_without_nav);
            $content_cleaned = preg_replace('/<a[^>]*class="[^"]*(\bbtn\b|\buc_more_btn\b)[^"]*"[^>]*>.*?<\/a>/s', '', $content_without_buttons);
            $content_cleaned = preg_replace('/<a[^>]*\s+class="[^"]*uc_more_btn[^"]*"[^>]*>.*?<\/a>/s', '', $content_cleaned);

            // Find all remaining links
            preg_match_all('/<a href="(.*?)".*?>(.*?)<\/a>/', $content_cleaned, $matches, PREG_SET_ORDER);

            foreach ($matches as $match) {
                $link = html_entity_decode($match[1]);
                $anchor_text = trim(strip_tags($match[2]));

                // Check exclusions based on settings and hardcoded rules
                if ($this->is_link_excluded($link, $anchor_text)) { continue; }

                $link_type = 'External';
                $target_title = '';
                $relevance_score = null;
                $target_post_id = null;
                $target_url_no_hash = null;

                // Check for special protocols first
                if (preg_match('/^mailto:/', $link)) {
                    $link_type = 'Email';
                }
                elseif (preg_match('/^tel:/', $link)) {
                    $link_type = 'Phone';
                }
                elseif (preg_match('/^sms:/', $link)) {
                    $link_type = 'SMS';
                }
                // Check if it's a jump link (starts with # only)
                elseif (strpos($link, '#') === 0) {
                    $link_type = 'Jump Link';
                }
                 // Check if it's an internal link (starts with site URL or /)
                elseif (strpos($link, $site_url) === 0 || strpos($link, '/') === 0) {
                    // Check if the internal link contains a hash
                    if (strpos($link, '#') !== false && strpos($link, $site_url . get_permalink($post->ID) . '#') !== 0 && strpos($link, get_permalink($post->ID) . '#') !== 0 && (strpos($link, '/') === 0 && strpos($link, parse_url(get_permalink($post->ID), PHP_URL_PATH) . '#') !==0) ) {
                         // This is an internal link to another page but with a hash (e.g. /other-page#section)
                         $link_type = 'Internal';
                    } elseif (strpos($link, '#') !== false) { // If it's a hash on the same page or just a hash
                        $link_type = 'Jump Link';
                    }
                     else {
                        $link_type = 'Internal';
                    }

                    // If truly internal (not just a jump link on the same page path)
                    if ($link_type === 'Internal') {
                        $target_url_for_id = $link;
                        if (strpos($link, '/') === 0) { // Relative URL like /my-page
                            $target_url_for_id = $site_url . $link;
                        }
                        // Remove hash for url_to_postid
                        $target_url_no_hash = strtok($target_url_for_id, '#');

                        $target_post_id = url_to_postid($target_url_no_hash);
                        if ($target_post_id) {
                            $target_title = get_the_title($target_post_id);
                            $source_title = $post->post_title; // Use post object title
                            $source_url = get_permalink($post->ID);

                            $relevance_score = $this->calculate_relevance_score($source_title, $target_title, $anchor_text, $source_url, $target_url_no_hash);
                        }
                    }
                }
                // Check for relative URLs that might be internal (and not special protocols)
                elseif (!preg_match('/^https?:\/\//', $link) && !preg_match('/^(mailto|tel|sms):/', $link)) {
                     if (strpos($link, '#') !== false) {
                        if (strpos(strtok($link, '#'), '/') !== false || strpos(strtok($link, '#'), '.') !== false) { // Heuristic for relative path
                            $link_type = 'Internal';
                             // Try to resolve relative URL for relevance
                            $target_url_for_id = rtrim($site_url, '/') . '/' . ltrim($link, '/');
                            $target_url_no_hash = strtok($target_url_for_id, '#');
                            $target_post_id = url_to_postid($target_url_no_hash);
                            if ($target_post_id) {
                                $target_title = get_the_title($target_post_id);
                                $source_title = $post->post_title;
                                $source_url = get_permalink($post->ID);
                                $relevance_score = $this->calculate_relevance_score($source_title, $target_title, $anchor_text, $source_url, $target_url_no_hash);
                            }

                        } else {
                            $link_type = 'Jump Link';
                        }
                    } else { // e.g., 'my-page.html' or 'another/path'
                        $link_type = 'Internal';
                         // Try to resolve relative URL for relevance
                        $target_url_for_id = rtrim($site_url, '/') . '/' . ltrim($link, '/');
                        $target_post_id = url_to_postid($target_url_for_id);
                        if ($target_post_id) {
                            $target_title = get_the_title($target_post_id);
                            $source_title = $post->post_title;
                            $source_url = get_permalink($post->ID);
                            $relevance_score = $this->calculate_relevance_score($source_title, $target_title, $anchor_text, $source_url, $target_url_for_id);
                        }
                    }
                }

                $results[] = [
                    'source_wp_post_id' => $post->ID,
                    'source_page_title' => $post->post_title,
                    'source_page_link'  => get_permalink($post->ID),
                    'target_hyperlink'  => $link,
                    'anchor_text'       => $anchor_text,
                    'link_type'         => $link_type,
                    'source_post_type'  => $post->post_type,
                    'target_page_title_if_internal' => $target_title,
                    'relevance_score'   => $relevance_score,
                ];
            }
        }
        return $results;
    }

    // --- Internal Link Helper Functions (from content-internal-links v3.2.1) ---

    private function get_excluded_texts() {
        $saved = $this->options['cil_excluded_texts'];
        if (empty($saved)) { return $this->get_default_excluded_texts(); }
        return array_filter(array_map('trim', explode("\n", $saved)));
    }

    private function get_excluded_urls() {
        $saved = $this->options['cil_excluded_urls'];
        if (empty($saved)) { return $this->get_default_excluded_urls(); }
        return array_filter(array_map('trim', explode("\n", $saved)));
    }

    private function get_excluded_post_types() {
        $saved = $this->options['cil_excluded_post_types'];
        if (empty($saved)) { return $this->get_default_excluded_post_types_system(); }
        $excluded = array_filter(array_map('trim', explode("\n", $saved)));
        // Always include the default system post types
        return array_unique(array_merge($this->get_default_excluded_post_types_system(), $excluded));
    }

    private function should_exclude_mailto() { return $this->options['cil_exclude_mailto'] === '1'; }
    private function should_exclude_tel() { return $this->options['cil_exclude_tel'] === '1'; }
    private function should_exclude_sms() { return $this->options['cil_exclude_sms'] === '1'; }

    private function url_matches_pattern($url, $pattern) {
        $url = trim($url); $pattern = trim($pattern);
        if (empty($pattern)) return false;
        $pattern = rtrim($pattern, '/');
        if (strpos($pattern, '*') !== false) {
            $regex_pattern = preg_quote($pattern, '#');
            $regex_pattern = str_replace('\*', '.*', $regex_pattern);
            return preg_match('#' . $regex_pattern . '#', $url) === 1;
        }
        return strpos($url, $pattern) !== false;
    }

    private function get_all_content_post_types() {
        $post_types = get_post_types(['public' => true], 'names');
        $private_post_types = get_post_types(['public' => false, '_builtin' => false], 'names');
        $all_post_types = array_merge($post_types, $private_post_types);
        $excluded_types = $this->get_excluded_post_types();
        return array_values(array_diff($all_post_types, $excluded_types));
    }

    private function is_special_protocol_excluded($link) {
        if ($this->should_exclude_mailto() && strpos($link, 'mailto:') === 0) return true;
        if ($this->should_exclude_tel() && strpos($link, 'tel:') === 0) return true;
        if ($this->should_exclude_sms() && strpos($link, 'sms:') === 0) return true;
        return false;
    }

    private function calculate_relevance_score($source_title, $target_title, $anchor_text, $source_url, $target_url) {
        $score = 0;
        
        // Define stop words here
        $stop_words = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as', 'of', 'from', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'shall', 'should', 'can', 'could', 'may', 'might', 'must', 'that', 'this', 'these', 'those', 'then', 'than', 'when', 'where', 'which', 'who', 'whom', 'whose', 'what', 'why', 'how'];
        
        // Normalize text for comparison
        $source_title_lower = strtolower($source_title);
        $target_title_lower = strtolower($target_title);
        $anchor_text_lower = strtolower($anchor_text);
        
        if (!empty($anchor_text_lower) && strpos($target_title_lower, $anchor_text_lower) !== false) {
            $score += 50;
        } else {
            // Check if anchor text contains words from target title
            $target_words = array_filter(str_word_count($target_title_lower, 1), function($word) use ($stop_words) {
                return strlen($word) > 3 && !in_array($word, $stop_words);
            });
            if (!empty($anchor_text_lower) && !empty($target_words)) {
                foreach ($target_words as $word) {
                    if (strpos($anchor_text_lower, $word) !== false) {
                        $score += 15; // Max 15 points
                        break;
                    }
                }
            }
        }

        // Check URL similarity (slugs)
        $source_slug = basename(parse_url($source_url, PHP_URL_PATH));
        $target_slug = basename(parse_url($target_url, PHP_URL_PATH));

        if (!empty($source_slug) && !empty($target_slug)) {
            similar_text($source_slug, $target_slug, $url_similarity_percent);
            if ($url_similarity_percent > 30) { // If slugs are more than 30% similar
                $score += min(20, $url_similarity_percent / 5); // Max 20 points (e.g. 100% similarity = 20 points)
            }
        }

         // Check title overlap (using the more detailed logic from v3.2.1)
        $source_words = array_filter(str_word_count($source_title_lower, 1), function($word) use ($stop_words) {
            return strlen($word) > 3 && !in_array($word, $stop_words);
        });
        $target_words_full = array_filter(str_word_count($target_title_lower, 1), function($word) use ($stop_words) {
            return strlen($word) > 3 && !in_array($word, $stop_words);
        });
        $common_words = array_intersect($source_words, $target_words_full);
        if (!empty($common_words)) {
            $score += min(25, count($common_words) * 5); // Max 25 points (adjusted from 40/20 in original)
        }

        return min(100, max(0, round($score, 2)));
    }

    private function is_link_excluded($link, $anchor_text) {
        $excluded_texts = $this->get_excluded_texts();
        $excluded_urls = $this->get_excluded_urls();

        // Check excluded texts
        if (in_array($anchor_text, $excluded_texts)) { return true; }

        // Check if special protocols should be excluded
        if ($this->is_special_protocol_excluded($link)) { return true; }

        // Always exclude pagination links (hardcoded)
        if (preg_match('/\/page\/\d+/', $link)) { return true; }

        // Check against user-defined excluded URL patterns
        foreach ($excluded_urls as $excluded_pattern) {
            if (!empty($excluded_pattern) && $this->url_matches_pattern($link, $excluded_pattern)) { return true; }
        }
        // Skip Elementor TOC and other TOC plugin links (hardcoded)
        if (preg_match('/#elementor-toc__heading-anchor|#ez-toc-|#toc-|#tableofcontents/', $link)) { return true; }
        return false;
    }

    // --- API Callback Functions ---

    public function get_site_data_callback(WP_REST_Request $request) {
        $publish_seo_data = $this->get_publish_seo_data();
        $internal_links_data = $this->get_internal_links_data_for_site();

        return new WP_REST_Response([
            'publish_seo_data' => $publish_seo_data,
            'internal_links_data' => $internal_links_data,
            'site_url' => home_url(),
            'generated_at' => current_time('mysql', true),
        ], 200);
    }

    public function trigger_external_script_callback(WP_REST_Request $request) {
        $trigger_url = $this->options['external_script_trigger_url'];
        $external_api_key = $this->options['external_script_api_key'];

        if (empty($trigger_url)) {
            return new WP_Error('trigger_url_not_set', 'External script trigger URL is not configured in plugin settings.', ['status' => 500]);
        }

        $payload = [
            'site_url' => home_url(),
            'site_id' => get_option('blogname') // Or any other unique site identifier
        ];

        $headers = ['Content-Type' => 'application/json'];
        if (!empty($external_api_key)) {
            $headers['X-External-API-Key'] = $external_api_key; // Or whatever header your Python script expects
        }

        // Use wp_remote_post to call the external script
        $response = wp_remote_post($trigger_url, [
            'method'    => 'POST',
            'headers'   => $headers,
            'body'      => json_encode($payload),
            'timeout'   => 45, // Increased timeout for potentially long-running external script
            'blocking'  => true, // Wait for the response
        ]);

        if (is_wp_error($response)) {
            return new WP_Error('external_script_trigger_failed', 'Failed to trigger external script: ' . $response->get_error_message(), ['status' => 502]);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        // Try to decode JSON, but return raw if not JSON
        $decoded_body = json_decode($response_body, true);

        return new WP_REST_Response([
            'status_code_from_external' => $response_code,
            'response_from_external' => ($decoded_body !== null) ? $decoded_body : $response_body,
        ], 200); // Return 200 from this endpoint, with external script's response nested
    }
}

// Initialize the plugin
new WP_Data_Exporter_Sync_Trigger();
?>
