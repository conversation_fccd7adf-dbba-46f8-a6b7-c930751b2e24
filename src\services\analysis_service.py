"""
Main SEO analysis service - orchestrates the entire analysis process
"""
import os
import asyncio
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse

from src.core.crawler import WebCrawler
from src.core.google_apis import GoogleSearchConsoleClient, GoogleAnalyticsClient
from src.core.wordpress import WordPressAPIClient
from src.database.supabase_client import SupabaseClient, SUPABASE_AVAILABLE
from src.services.link_analysis_service import LinkAnalysisService
from src.services.report_service import ReportService
from src.utils.file_utils import get_output_directory, save_urls_list, create_temp_json_file, cleanup_temp_file
from src.utils.logging import get_logger
from src.config.settings import settings

logger = get_logger(__name__)


class SEOAnalysisService:
    """Main service for orchestrating SEO analysis"""
    
    def __init__(self):
        self.crawler = WebCrawler()
        self.link_analyzer = LinkAnalysisService()
        self.report_service = ReportService()
    
    async def run_analysis(self, config: Dict[str, Any], task_id: str, 
                          progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Run complete SEO analysis
        
        Args:
            config: Analysis configuration
            task_id: Unique task identifier
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict containing analysis results
        """
        try:
            # Initialize progress tracking
            if progress_callback:
                progress_callback(task_id, 0, "Starting analysis...")
            
            # Extract configuration
            domain_property = config['domain_property']
            ga_property_id = config['ga_property_id']
            service_account_file = config.get('service_account_file')
            homepage = config.get('homepage') or domain_property
            start_date = config.get('start_date')
            end_date = config.get('end_date')
            website_urls = config.get('website_urls', [])
            wp_api_key = config.get('wp_api_key')
            incremental = config.get('incremental', False)
            wipe_existing = config.get('wipe_existing', False)

            # Use environment variables as fallback for Supabase credentials
            supabase_url = config.get('supabase_url') or settings.supabase_url
            supabase_key = config.get('supabase_key') or settings.supabase_key

            if not supabase_url or not supabase_key:
                raise ValueError("Supabase credentials not provided in config or environment variables")
            
            # Set default dates if not provided
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            
            # Create session-specific output directory to prevent conflicts
            output_dir = get_output_directory(domain_property, session_id=task_id)
            
            if progress_callback:
                progress_callback(task_id, 10, "Initializing Google API clients...")
            
            # Initialize Google API clients
            gsc_client = GoogleSearchConsoleClient(service_account_file)
            ga_client = GoogleAnalyticsClient(service_account_file)
            
            # Try WordPress API first (more efficient than crawling)
            wp_data = None
            crawl_results = []

            if wp_api_key:
                if progress_callback:
                    progress_callback(task_id, 20, "Checking WordPress API...")

                wp_client = WordPressAPIClient(wp_api_key)
                wp_api_url = wp_client.detect_wp_api(domain_property, homepage)
                
                if wp_api_url:
                    wp_data = wp_client.fetch_data(wp_api_url)
                    if wp_data and ('pages_data' in wp_data or 'publish_seo_data' in wp_data):
                        # Support both data structure formats
                        crawl_results = wp_data.get('pages_data') or wp_data.get('publish_seo_data', [])
                        logger.info(f"WordPress API provided {len(crawl_results)} pages")

                        # Check if WordPress data is complete (has essential SEO fields)
                        if crawl_results and not self._is_wordpress_data_complete(crawl_results):
                            logger.warning("WordPress API data is incomplete (missing content/SEO fields), falling back to crawling")
                            crawl_results = []  # Force crawling

            # Crawl website if no WordPress data, limited data, or incomplete data
            if not crawl_results or len(crawl_results) <= 1:
                if progress_callback:
                    progress_callback(task_id, 25, "Discovering website URLs...")

                # Get website URLs only when needed for crawling
                if not website_urls:
                    website_urls = await self.crawler.discover_urls_from_homepage(homepage)

                save_urls_list(website_urls, output_dir)

                if progress_callback:
                    progress_callback(task_id, 35, f"Crawling {len(website_urls)} URLs...")
                logger.info(f"Falling back to website crawling. WordPress data: {len(crawl_results) if crawl_results else 0} pages")
                crawl_results = await self.crawler.crawl_site(website_urls, output_dir)
            else:
                if progress_callback:
                    progress_callback(task_id, 35, f"Using WordPress API data for {len(crawl_results)} pages...")
                logger.info(f"Using WordPress API data instead of crawling. Found {len(crawl_results)} pages")
            
            # Smart date range calculation for incremental analysis
            gsc_start_date, gsc_end_date = self._calculate_smart_date_range(
                supabase_client, start_date, end_date, incremental
            )

            if progress_callback:
                if incremental and gsc_start_date != start_date:
                    progress_callback(task_id, 50, f"Fetching Google data (smart incremental: {gsc_start_date} to {gsc_end_date})...")
                else:
                    progress_callback(task_id, 50, "Fetching Google Search Console data...")

            # Fetch GSC data with smart date range
            logger.info(f"Fetching GSC data for date range: {gsc_start_date} to {gsc_end_date}")
            keywords_df = gsc_client.get_data_by_month(
                domain_property, gsc_start_date, gsc_end_date,
                dimensions=['query', 'page'],
                metrics=['clicks', 'impressions', 'ctr', 'position']
            )

            hist_traffic_df = gsc_client.get_data_by_month(
                domain_property, gsc_start_date, gsc_end_date,
                dimensions=['page'],
                metrics=['clicks', 'impressions', 'ctr', 'position']
            )

            logger.info(f"GSC data fetched: {len(keywords_df)} keywords, {len(hist_traffic_df)} traffic records")

            # Check if we have any new data to process
            if incremental and gsc_start_date == gsc_end_date and keywords_df.empty and hist_traffic_df.empty:
                logger.info("No new GSC data found for incremental analysis")

            if progress_callback:
                progress_callback(task_id, 65, "Fetching Google Analytics data...")

            # Fetch GA data with smart date range and metrics that match database schema
            ga_df = ga_client.get_data_by_month(
                ga_property_id, gsc_start_date, gsc_end_date,
                dimensions=['pagePath'],
                metrics=['screenPageViews', 'activeUsers']
            )
            
            if progress_callback:
                progress_callback(task_id, 75, "Analyzing internal links...")
            
            # Use WordPress data from earlier fetch (avoid duplicate API calls)
            wp_internal_links_data = wp_data if wp_data else None

            # Create a basic data_df for internal links analysis
            # (The link analyzer expects data_df as second parameter, not keywords_df)
            if crawl_results:
                # Handle both CrawlResult objects and dictionaries from WordPress API
                data_rows = []
                for result in crawl_results:
                    if hasattr(result, 'url'):
                        # CrawlResult object from crawler
                        url = result.url
                    elif isinstance(result, dict):
                        # Dictionary from WordPress API
                        url = result.get('url') or result.get('URL', '')
                    else:
                        # Fallback
                        url = str(result)

                    data_rows.append({
                        'URL': url,
                        'Topic': '',  # Placeholder
                    })

                data_df = pd.DataFrame(data_rows)
            else:
                data_df = pd.DataFrame(columns=['URL', 'Topic'])

            internal_links_df = self.link_analyzer.build_internal_links_sheet(
                crawl_results, data_df, wp_internal_links_data
            )
            
            if progress_callback:
                progress_callback(task_id, 85, "Saving data to Supabase...")

            # Always save to Supabase (required)
            if not SUPABASE_AVAILABLE:
                raise Exception("Supabase client not available. Install with: pip install supabase")

            domain = urlparse(domain_property).netloc
            supabase_client = SupabaseClient(
                url=supabase_url,
                key=supabase_key,
                domain=domain
            )

            # Convert crawl results to DataFrame for Supabase
            # Handle both CrawlResult objects and dictionaries from WordPress API
            data_rows = []
            for result in crawl_results:
                if hasattr(result, 'dict'):
                    # CrawlResult object from crawler
                    data_rows.append(result.dict())
                elif isinstance(result, dict):
                    # Dictionary from WordPress API - filter to only valid columns
                    filtered_result = {}

                    # Map WordPress API columns to database schema
                    column_mapping = {
                        'url': 'URL',
                        'title': 'SEO Title',
                        'description': 'Meta Description',
                        'h1': 'H1',
                        'text': 'Page Content',
                        'content': 'Page Content',  # Alternative content field
                        'focus_keyphrase': 'Focus Keyword',
                        'type': 'Page Type',
                        'category': 'Topic'
                    }

                    # Only include columns that exist in database schema
                    for wp_col, db_col in column_mapping.items():
                        if wp_col in result:
                            filtered_result[db_col] = result[wp_col]

                    # Ensure we have at least a URL
                    if 'URL' not in filtered_result and 'url' in result:
                        filtered_result['URL'] = result['url']

                    data_rows.append(filtered_result)
                else:
                    # Fallback - convert to basic dict
                    data_rows.append({'URL': str(result)})

            data_df = pd.DataFrame(data_rows)

            # Additional column renaming for any remaining mismatches
            if not data_df.empty:
                data_df = data_df.rename(columns={
                    'url': 'URL',
                    'title': 'SEO Title',
                    'description': 'Meta Description',
                    'h1': 'H1',
                    'text': 'Page Content'
                })

            # Save all data to Supabase with mode support
            if wipe_existing:
                if progress_callback:
                    progress_callback(task_id, 85, "Saving data (reanalyze mode - wiping old data)...")
            elif incremental:
                if progress_callback:
                    # Show smart incremental benefits
                    date_range_info = f" ({gsc_start_date} to {gsc_end_date})" if gsc_start_date != start_date else ""
                    progress_callback(task_id, 85, f"Saving data (smart incremental{date_range_info})...")
            else:
                if progress_callback:
                    progress_callback(task_id, 85, "Saving data (full mode - creating new snapshot)...")

            supabase_client.save_pages_data(data_df, incremental=incremental, wipe_existing=wipe_existing)
            supabase_client.save_gsc_keywords(keywords_df)
            supabase_client.save_gsc_traffic(hist_traffic_df)
            supabase_client.save_internal_links(internal_links_df)

            if not ga_df.empty:
                supabase_client.save_ga_data(ga_df)
            
            # Update last analysis date for smart incremental tracking
            self._update_last_analysis_date(supabase_client)

            if progress_callback:
                progress_callback(task_id, 100, "Analysis completed and data saved to Supabase!")

            # Return results
            result = {
                'domain': domain,
                'site_id': supabase_client.site_id,
                'pages_analyzed': len(crawl_results),
                'keywords_found': len(keywords_df),
                'internal_links': len(internal_links_df),
                'traffic_records': len(hist_traffic_df),
                'analytics_records': len(ga_df) if not ga_df.empty else 0,
                'supabase_url': supabase_url,
                'analysis_date': datetime.now().isoformat()
            }

            return result
            
        except Exception as e:
            logger.exception(f"Error in SEO analysis for task {task_id}")
            if progress_callback:
                progress_callback(task_id, -1, f"Analysis failed: {str(e)}")
            raise

    def _is_wordpress_data_complete(self, wp_data: List[dict]) -> bool:
        """Check if WordPress API data contains essential SEO content fields"""
        if not wp_data:
            return False

        # Check a sample of pages for essential content fields
        sample_size = min(3, len(wp_data))
        sample_pages = wp_data[:sample_size]

        essential_fields = ['description', 'h1', 'text', 'content']

        for page in sample_pages:
            # Check if at least one essential content field has meaningful data
            has_content = False
            for field in essential_fields:
                if field in page and page[field] and len(str(page[field]).strip()) > 10:
                    has_content = True
                    break

            if not has_content:
                logger.debug(f"Page {page.get('url', 'unknown')} missing essential content fields")
                return False

        logger.info("WordPress API data appears complete with SEO content")
        return True

    def _calculate_smart_date_range(self, supabase_client, config_start_date: str, config_end_date: str, incremental: bool) -> tuple:
        """Calculate smart date range for incremental analysis"""
        if not incremental:
            # Full analysis: use provided date range
            return config_start_date, config_end_date

        try:
            # Get last analysis date for this site
            from supabase import create_client
            client = create_client(supabase_client.url, supabase_client.key)

            site_response = client.table('sites').select('last_analysis_date').eq('id', supabase_client.db_id).execute()

            if site_response.data and site_response.data[0].get('last_analysis_date'):
                last_analysis_date = site_response.data[0]['last_analysis_date']

                # Parse the last analysis date
                from datetime import datetime, timedelta
                last_date = datetime.fromisoformat(last_analysis_date.replace('Z', '+00:00'))

                # Start from the day after last analysis to avoid duplicates
                smart_start_date = (last_date + timedelta(days=1)).strftime('%Y-%m-%d')

                # Use today as end date for incremental
                today = datetime.now().strftime('%Y-%m-%d')

                # If smart start date is after today, no new data to fetch
                if smart_start_date > today:
                    logger.info(f"No new data to fetch - last analysis: {last_analysis_date}, today: {today}")
                    return smart_start_date, smart_start_date  # Empty range

                logger.info(f"Smart incremental range: {smart_start_date} to {today} (last analysis: {last_analysis_date})")
                return smart_start_date, today

            else:
                # No previous analysis, use a reasonable default (last 30 days)
                from datetime import datetime, timedelta
                today = datetime.now()
                thirty_days_ago = (today - timedelta(days=30)).strftime('%Y-%m-%d')
                today_str = today.strftime('%Y-%m-%d')

                logger.info(f"No previous analysis found, using default range: {thirty_days_ago} to {today_str}")
                return thirty_days_ago, today_str

        except Exception as e:
            logger.warning(f"Error calculating smart date range: {e}, falling back to provided range")
            return config_start_date, config_end_date

    def _update_last_analysis_date(self, supabase_client):
        """Update the last analysis date for this site"""
        try:
            from supabase import create_client
            from datetime import datetime

            client = create_client(supabase_client.url, supabase_client.key)
            current_time = datetime.now().isoformat()

            response = client.table('sites').update({
                'last_analysis_date': current_time
            }).eq('id', supabase_client.db_id).execute()

            logger.info(f"Updated last analysis date to {current_time} for site {supabase_client.db_id}")

        except Exception as e:
            logger.warning(f"Failed to update last analysis date: {e}")
